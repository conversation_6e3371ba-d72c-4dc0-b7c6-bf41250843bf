# Kenyan Laboratory CPD Provider - Static Course Data

## Overview

This document describes the realistic static data implemented for a Kenyan laboratory Continuing Professional Development (CPD) provider that is certified by the KMLTTB (Kenya Medical Laboratory Technicians and Technologists Board).

## Implementation

The static data is automatically used when there are no courses in the database. Once real courses are added to the database, the system will switch to using the actual database data.

### Controllers Updated

1. **AdminCourseController** (`app/Http/Controllers/Admin/CourseController.php`)
   - Added `getStaticCourseData()` method
   - Added `getStaticCategoryData()` method
   - Modified `index()` method to use static data when database is empty

2. **HomeController** (`app/Http/Controllers/HomeController.php`)
   - Added same static data methods
   - Modified `index()` method to use static data for frontend display

## Course Categories

The static data includes 6 professional laboratory categories:

1. **Clinical Chemistry** - Biochemical analysis, enzyme studies, and clinical chemistry techniques
2. **Hematology** - Blood analysis, coagulation studies, and transfusion medicine
3. **Microbiology** - Pathogen identification, antimicrobial testing, and infection control
4. **Quality Management** - ISO standards, quality systems, and laboratory accreditation
5. **Immunology** - Immunoassays, serology, and diagnostic immunology
6. **Laboratory Safety** - Safety protocols, biosecurity, and emergency procedures

## Sample Courses

### 1. Clinical Chemistry Fundamentals for KMLTTB Certification
- **Duration**: 40 hours
- **Level**: Intermediate
- **Price**: KES 8,500 (Discounted: KES 6,800)
- **Status**: Published, Featured
- **Instructor**: Dr. Mary Wanjiku, PhD

### 2. Hematology and Blood Banking Essentials
- **Duration**: 45 hours
- **Level**: Intermediate
- **Price**: KES 9,200 (Discounted: KES 7,360)
- **Status**: Published, Featured
- **Instructor**: Dr. James Kiprotich, MSc

### 3. Medical Microbiology and Infection Control
- **Duration**: 50 hours
- **Level**: Advanced
- **Price**: KES 10,500 (Discounted: KES 8,400)
- **Status**: Published
- **Instructor**: Dr. Grace Muthoni, PhD

### 4. Laboratory Quality Management Systems
- **Duration**: 35 hours
- **Level**: Advanced
- **Price**: KES 7,800 (Discounted: KES 6,240)
- **Status**: Published
- **Instructor**: Dr. Peter Otieno, MSc QM

### 5. Immunology and Serology Techniques
- **Duration**: 42 hours
- **Level**: Intermediate
- **Price**: KES 8,900 (Discounted: KES 7,120)
- **Status**: Published
- **Instructor**: Dr. Sarah Njeri, PhD

### 6. Laboratory Safety and Biosecurity Fundamentals
- **Duration**: 25 hours
- **Level**: Beginner
- **Price**: FREE
- **Status**: Published, Featured
- **Instructor**: Dr. Michael Ochieng, MSc Safety

## Key Features

### Authentic Kenyan Context
- All courses are designed for KMLTTB certification requirements
- Pricing in Kenyan Shillings (KES) with affordable rates for local professionals
- Instructor names reflect Kenyan naming conventions
- Course content addresses local laboratory challenges and standards

### Professional Standards
- Courses align with international standards (ISO 15189, WHO guidelines)
- Emphasis on quality management and safety protocols
- Continuing education credits for KMLTTB compliance
- Certificate-bearing courses for professional development

### Accessibility Features
- One free course (Laboratory Safety) to ensure basic safety training access
- Discounted pricing on all paid courses
- Online delivery format suitable for working professionals
- Flexible duration options (25-50 hours)

## Pricing Structure

The pricing is designed to be affordable for Kenyan laboratory professionals:

- **Free Course**: Laboratory Safety (KES 0)
- **Entry Level**: Quality Management (KES 6,240 - 7,800)
- **Standard Courses**: Clinical Chemistry, Hematology, Immunology (KES 6,800 - 7,360)
- **Advanced Course**: Microbiology (KES 8,400 - 10,500)

All paid courses include 20% discount pricing to make them more accessible.

## Technical Implementation

### Data Structure
- Courses are stored as associative arrays with all required fields
- Categories include color coding and FontAwesome icons
- Instructor information includes realistic Kenyan names and email addresses
- All courses have proper slugs, meta descriptions, and SEO-friendly content

### View Compatibility
- Static data is converted to objects for seamless integration with existing Blade templates
- Pricing displays use KES currency format
- All existing view functionality works without modification

## Usage

The static data is automatically loaded when:
1. The database has no courses (`Course::count() === 0`)
2. Users visit the admin courses page or public course listings
3. The system needs to display course categories or statistics

To switch to real data:
1. Add actual courses through the admin interface
2. The system will automatically detect real data and stop using static data
3. Static data methods remain available for reference or testing

## Compliance

This static data ensures:
- KMLTTB certification alignment
- Professional medical laboratory standards
- Kenyan healthcare sector relevance
- Affordable continuing education options
- Quality assurance and safety emphasis
