<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Course;
use App\Models\Category;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class CourseController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        // Check if we have real data in the database
        $hasRealData = Course::count() > 0;

        if ($hasRealData) {
            // Use real data from database
            $courses = Course::with(['category', 'instructor'])
                ->orderBy('created_at', 'desc')
                ->paginate(10);

            $categories = Category::where('is_active', true)
                ->withCount('courses')
                ->orderBy('courses_count', 'desc')
                ->get();

            // Statistics
            $totalCourses = Course::count();
            $publishedCourses = Course::where('status', 'published')->count();
            $draftCourses = Course::where('status', 'draft')->count();
            $featuredCourses = Course::where('is_featured', true)->count();
        } else {
            // Use static demo data for Kenyan Laboratory CPD Provider
            $staticCourses = $this->getStaticCourseData();
            $categories = $this->getStaticCategoryData();

            // Convert arrays to objects for view compatibility
            $courses = collect($staticCourses)->map(function($course) {
                return (object) $course;
            });

            // Static statistics
            $totalCourses = count($staticCourses);
            $publishedCourses = count(array_filter($staticCourses, fn($course) => $course['status'] === 'published'));
            $draftCourses = count(array_filter($staticCourses, fn($course) => $course['status'] === 'draft'));
            $featuredCourses = count(array_filter($staticCourses, fn($course) => $course['is_featured']));
        }

        return view('admin.courses.index', compact(
            'courses',
            'categories',
            'totalCourses',
            'publishedCourses',
            'draftCourses',
            'featuredCourses'
        ));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $categories = Category::where('is_active', true)
            ->orderBy('name')
            ->get();

        $instructors = User::where('role', 'admin')
            ->orderBy('name')
            ->get();

        return view('admin.courses.create', compact('categories', 'instructors'));
    }

    /**
     * Show the debug form for testing course creation.
     */
    public function debug()
    {
        $categories = Category::where('is_active', true)
            ->orderBy('name')
            ->get();

        $instructors = User::where('role', 'admin')
            ->orderBy('name')
            ->get();

        return view('admin.courses.debug', compact('categories', 'instructors'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:courses,slug',
            'short_description' => 'nullable|string|max:500',
            'description' => 'nullable|string',
            'learning_objectives' => 'nullable|string',
            'requirements' => 'nullable|string',
            'category_id' => 'required|integer',
            'instructor_id' => 'required|integer',
            'status' => 'required|in:draft,published,archived',
            'duration' => 'nullable|numeric|min:0',
            'difficulty_level' => 'nullable|in:beginner,intermediate,advanced',
            'language' => 'nullable|string',
            'certificate' => 'nullable|boolean',
            'is_featured' => 'nullable|boolean',
            'is_free' => 'nullable|boolean',
            'price' => 'nullable|numeric|min:0',
            'discount_price' => 'nullable|numeric|min:0',
            'thumbnail' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'preview_video' => 'nullable|url',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'tags' => 'nullable|string',
        ]);

        try {
            // Generate slug if not provided
            $slug = $request->slug ?: Str::slug($request->title);

            // Handle thumbnail upload
            $thumbnailPath = null;
            if ($request->hasFile('thumbnail')) {
                $thumbnailPath = $request->file('thumbnail')->store('courses', 'public');
            }

            // Validate pricing logic
            if (!$request->has('is_free')) {
                if ($request->discount_price && $request->discount_price >= $request->price) {
                    return redirect()->back()
                        ->withInput()
                        ->with('error', 'Discount price must be less than regular price.');
                }
            }

            // Create the course
            $course = Course::create([
                'title' => $request->title,
                'slug' => $slug,
                'short_description' => $request->short_description,
                'description' => $request->description,
                'learning_objectives' => $request->learning_objectives,
                'requirements' => $request->requirements,
                'category_id' => $request->category_id,
                'instructor_id' => $request->instructor_id,
                'status' => $request->status,
                'duration' => $request->duration,
                'difficulty_level' => $request->difficulty_level,
                'language' => $request->language,
                'certificate' => $request->has('certificate'),
                'is_featured' => $request->has('is_featured'),
                'is_free' => $request->has('is_free'),
                'price' => $request->has('is_free') ? 0 : $request->price,
                'discount_price' => $request->has('is_free') ? 0 : $request->discount_price,
                'thumbnail' => $thumbnailPath,
                'preview_video' => $request->preview_video,
                'meta_title' => $request->meta_title,
                'meta_description' => $request->meta_description,
                'tags' => $request->tags,
                'created_by' => Auth::id(),
                'updated_by' => Auth::id(),
            ]);

            return redirect()->route('admin.courses.index')
                ->with('success', 'Course created successfully!');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Error creating course: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        // For now, redirect to edit
        return redirect()->route('admin.courses.edit', $id);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        try {
            $course = Course::with(['category', 'instructor'])->findOrFail($id);

            $categories = Category::where('is_active', true)
                ->orderBy('name')
                ->get();

            $instructors = User::where('role', 'admin')
                ->orderBy('name')
                ->get();

            return view('admin.courses.edit', compact('course', 'categories', 'instructors'));
        } catch (\Exception $e) {
            return redirect()->route('admin.courses.index')
                ->with('error', 'Course not found.');
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:courses,slug,' . $id,
            'short_description' => 'nullable|string|max:500',
            'description' => 'nullable|string',
            'learning_objectives' => 'nullable|string',
            'requirements' => 'nullable|string',
            'category_id' => 'required|integer',
            'instructor_id' => 'required|integer',
            'status' => 'required|in:draft,published,archived',
            'duration' => 'nullable|numeric|min:0',
            'difficulty_level' => 'nullable|in:beginner,intermediate,advanced',
            'language' => 'nullable|string',
            'certificate' => 'nullable|boolean',
            'is_featured' => 'nullable|boolean',
            'is_free' => 'nullable|boolean',
            'price' => 'nullable|numeric|min:0',
            'discount_price' => 'nullable|numeric|min:0',
            'thumbnail' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'preview_video' => 'nullable|url',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'tags' => 'nullable|string',
        ]);

        try {
            $course = Course::findOrFail($id);

            // Generate slug if not provided
            $slug = $request->slug ?: Str::slug($request->title);

            // Handle thumbnail upload
            $thumbnailPath = $course->thumbnail; // Keep existing thumbnail by default
            if ($request->hasFile('thumbnail')) {
                // Delete old thumbnail if it exists
                if ($course->thumbnail && Storage::disk('public')->exists($course->thumbnail)) {
                    Storage::disk('public')->delete($course->thumbnail);
                }
                $thumbnailPath = $request->file('thumbnail')->store('courses', 'public');
            }

            // Validate pricing logic
            if (!$request->has('is_free')) {
                if ($request->discount_price && $request->discount_price >= $request->price) {
                    return redirect()->back()
                        ->withInput()
                        ->with('error', 'Discount price must be less than regular price.');
                }
            }

            // Update the course
            $course->update([
                'title' => $request->title,
                'slug' => $slug,
                'short_description' => $request->short_description,
                'description' => $request->description,
                'learning_objectives' => $request->learning_objectives,
                'requirements' => $request->requirements,
                'category_id' => $request->category_id,
                'instructor_id' => $request->instructor_id,
                'status' => $request->status,
                'duration' => $request->duration,
                'difficulty_level' => $request->difficulty_level,
                'language' => $request->language,
                'certificate' => $request->has('certificate'),
                'is_featured' => $request->has('is_featured'),
                'is_free' => $request->has('is_free'),
                'price' => $request->has('is_free') ? 0 : $request->price,
                'discount_price' => $request->has('is_free') ? 0 : $request->discount_price,
                'thumbnail' => $thumbnailPath,
                'preview_video' => $request->preview_video,
                'meta_title' => $request->meta_title,
                'meta_description' => $request->meta_description,
                'tags' => $request->tags,
                'updated_by' => Auth::id(),
            ]);

            return redirect()->route('admin.courses.index')
                ->with('success', 'Course updated successfully!');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Error updating course: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            // Here you would typically delete from database
            // Example:
            // $course = Course::findOrFail($id);
            // $course->delete();

            return redirect()->route('admin.courses.index')
                ->with('success', 'Course deleted successfully!');

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Error deleting course: ' . $e->getMessage());
        }
    }

    /**
     * Handle bulk actions for courses.
     */
    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:publish,draft,delete',
            'course_ids' => 'required|array',
            'course_ids.*' => 'integer',
        ]);

        try {
            $action = $request->action;
            $courseIds = $request->course_ids;
            $count = count($courseIds);

            // Here you would typically perform bulk operations on database
            // Example:
            // switch ($action) {
            //     case 'publish':
            //         Course::whereIn('id', $courseIds)->update(['status' => 'published']);
            //         break;
            //     case 'draft':
            //         Course::whereIn('id', $courseIds)->update(['status' => 'draft']);
            //         break;
            //     case 'delete':
            //         Course::whereIn('id', $courseIds)->delete();
            //         break;
            // }

            $actionText = match($action) {
                'publish' => 'published',
                'draft' => 'moved to draft',
                'delete' => 'deleted',
            };

            return redirect()->route('admin.courses.index')
                ->with('success', "{$count} courses {$actionText} successfully!");

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Error performing bulk action: ' . $e->getMessage());
        }
    }

    /**
     * Create a new category via AJAX
     */
    public function createCategoryAjax(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:500',
            'color' => 'nullable|string|size:7|regex:/^#[0-9A-Fa-f]{6}$/',
            'icon' => 'nullable|string|max:255',
        ]);

        try {
            $category = Category::create([
                'name' => $request->name,
                'slug' => Str::slug($request->name),
                'description' => $request->description,
                'color' => $request->color ?: '#6777ef',
                'icon' => $request->icon,
                'is_active' => true,
                'sort_order' => 0,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Category created successfully!',
                'category' => [
                    'id' => $category->id,
                    'name' => $category->name,
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error creating category: ' . $e->getMessage()
            ], 422);
        }
    }

    /**
     * Create a new instructor via AJAX
     */
    public function createInstructorAjax(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'password' => 'required|string|min:8',
        ]);

        try {
            $instructor = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'password' => bcrypt($request->password),
                'role' => 'admin', // Instructors are admin users
                'email_verified_at' => now(),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Instructor created successfully!',
                'instructor' => [
                    'id' => $instructor->id,
                    'name' => $instructor->name,
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error creating instructor: ' . $e->getMessage()
            ], 422);
        }
    }

    /**
     * Get static course data for Kenyan Laboratory CPD Provider
     */
    private function getStaticCourseData()
    {
        return [
            [
                'id' => 1,
                'title' => 'Clinical Chemistry Fundamentals for KMLTTB Certification',
                'slug' => 'clinical-chemistry-fundamentals-kmlttb',
                'short_description' => 'Comprehensive online course covering clinical chemistry principles, quality control, and laboratory safety standards as required by KMLTTB.',
                'description' => 'This course provides essential knowledge in clinical chemistry for medical laboratory professionals seeking KMLTTB certification. Topics include biochemical analysis, enzyme studies, lipid profiles, liver function tests, kidney function assessment, and quality assurance protocols. Designed specifically for Kenyan laboratory technicians and technologists.',
                'learning_objectives' => "• Master clinical chemistry analytical techniques\n• Understand quality control procedures\n• Apply KMLTTB safety standards\n• Interpret biochemical test results\n• Implement laboratory best practices",
                'requirements' => 'Basic laboratory experience, Certificate/Diploma in Medical Laboratory Technology, Internet connection for online learning',
                'category' => (object)['id' => 1, 'name' => 'Clinical Chemistry', 'slug' => 'clinical-chemistry'],
                'instructor' => (object)['id' => 1, 'name' => 'Dr. Mary Wanjiku, PhD', 'email' => '<EMAIL>'],
                'status' => 'published',
                'duration' => 40.00,
                'difficulty_level' => 'intermediate',
                'language' => 'English',
                'certificate' => true,
                'is_featured' => true,
                'is_free' => false,
                'price' => 8500.00,
                'discount_price' => 6800.00,
                'thumbnail' => null,
                'preview_video' => null,
                'meta_title' => 'Clinical Chemistry KMLTTB Certification Course',
                'meta_description' => 'Professional clinical chemistry course for KMLTTB certification. Learn biochemical analysis, quality control, and laboratory safety.',
                'tags' => 'clinical chemistry,KMLTTB,biochemistry,laboratory,certification,medical technology',
                'created_at' => '2024-01-15 08:00:00',
                'updated_at' => '2024-01-15 08:00:00',
            ],
            [
                'id' => 2,
                'title' => 'Hematology and Blood Banking Essentials',
                'slug' => 'hematology-blood-banking-essentials',
                'short_description' => 'Master hematological testing, blood grouping, cross-matching, and transfusion medicine according to KMLTTB standards.',
                'description' => 'Comprehensive training in hematology and blood banking for medical laboratory professionals. This course covers complete blood count interpretation, blood film examination, coagulation studies, blood grouping systems, compatibility testing, and transfusion reactions. Aligned with KMLTTB competency requirements.',
                'learning_objectives' => "• Perform accurate hematological analyses\n• Master blood grouping and cross-matching\n• Understand coagulation mechanisms\n• Identify blood disorders\n• Apply transfusion medicine principles",
                'requirements' => 'Medical Laboratory Technology background, Basic hematology knowledge, Reliable internet connection',
                'category' => (object)['id' => 2, 'name' => 'Hematology', 'slug' => 'hematology'],
                'instructor' => (object)['id' => 2, 'name' => 'Dr. James Kiprotich, MSc', 'email' => '<EMAIL>'],
                'status' => 'published',
                'duration' => 45.00,
                'difficulty_level' => 'intermediate',
                'language' => 'English',
                'certificate' => true,
                'is_featured' => true,
                'is_free' => false,
                'price' => 9200.00,
                'discount_price' => 7360.00,
                'thumbnail' => null,
                'preview_video' => null,
                'meta_title' => 'Hematology Blood Banking KMLTTB Course',
                'meta_description' => 'Professional hematology and blood banking course for KMLTTB certification. Master blood testing and transfusion medicine.',
                'tags' => 'hematology,blood banking,transfusion,KMLTTB,medical laboratory,blood grouping',
                'created_at' => '2024-01-20 09:00:00',
                'updated_at' => '2024-01-20 09:00:00',
            ],
            [
                'id' => 3,
                'title' => 'Medical Microbiology and Infection Control',
                'slug' => 'medical-microbiology-infection-control',
                'short_description' => 'Learn bacterial identification, antimicrobial susceptibility testing, and infection prevention protocols for KMLTTB compliance.',
                'description' => 'Essential microbiology course for laboratory professionals covering bacterial, viral, fungal, and parasitic identification. Includes antimicrobial susceptibility testing, infection control measures, biosafety protocols, and emerging infectious diseases relevant to Kenya. Meets KMLTTB continuing education requirements.',
                'learning_objectives' => "• Identify pathogenic microorganisms\n• Perform antimicrobial susceptibility testing\n• Implement infection control measures\n• Apply biosafety protocols\n• Understand emerging infectious diseases",
                'requirements' => 'Basic microbiology knowledge, Laboratory experience, Internet access for online modules',
                'category' => (object)['id' => 3, 'name' => 'Microbiology', 'slug' => 'microbiology'],
                'instructor' => (object)['id' => 3, 'name' => 'Dr. Grace Muthoni, PhD', 'email' => '<EMAIL>'],
                'status' => 'published',
                'duration' => 50.00,
                'difficulty_level' => 'advanced',
                'language' => 'English',
                'certificate' => true,
                'is_featured' => false,
                'is_free' => false,
                'price' => 10500.00,
                'discount_price' => 8400.00,
                'thumbnail' => null,
                'preview_video' => null,
                'meta_title' => 'Medical Microbiology KMLTTB Certification',
                'meta_description' => 'Advanced microbiology course for KMLTTB certification. Learn pathogen identification and infection control.',
                'tags' => 'microbiology,infection control,antimicrobial,KMLTTB,pathogen identification,biosafety',
                'created_at' => '2024-01-25 10:00:00',
                'updated_at' => '2024-01-25 10:00:00',
            ],
            [
                'id' => 4,
                'title' => 'Laboratory Quality Management Systems',
                'slug' => 'laboratory-quality-management-systems',
                'short_description' => 'Implement ISO 15189 standards and quality management systems in medical laboratories according to KMLTTB guidelines.',
                'description' => 'Comprehensive course on laboratory quality management covering ISO 15189 implementation, document control, internal audits, corrective actions, and continuous improvement. Essential for laboratory managers and senior technologists seeking KMLTTB compliance and accreditation.',
                'learning_objectives' => "• Understand ISO 15189 requirements\n• Implement quality management systems\n• Conduct internal audits\n• Manage laboratory documentation\n• Ensure regulatory compliance",
                'requirements' => 'Laboratory management experience, Understanding of quality concepts, Computer literacy',
                'category' => (object)['id' => 4, 'name' => 'Quality Management', 'slug' => 'quality-management'],
                'instructor' => (object)['id' => 4, 'name' => 'Dr. Peter Otieno, MSc QM', 'email' => '<EMAIL>'],
                'status' => 'published',
                'duration' => 35.00,
                'difficulty_level' => 'advanced',
                'language' => 'English',
                'certificate' => true,
                'is_featured' => false,
                'is_free' => false,
                'price' => 7800.00,
                'discount_price' => 6240.00,
                'thumbnail' => null,
                'preview_video' => null,
                'meta_title' => 'Laboratory Quality Management ISO 15189',
                'meta_description' => 'Master laboratory quality management systems and ISO 15189 standards for KMLTTB compliance.',
                'tags' => 'quality management,ISO 15189,laboratory accreditation,KMLTTB,quality systems',
                'created_at' => '2024-02-01 11:00:00',
                'updated_at' => '2024-02-01 11:00:00',
            ],
            [
                'id' => 5,
                'title' => 'Immunology and Serology Techniques',
                'slug' => 'immunology-serology-techniques',
                'short_description' => 'Master immunological testing methods, ELISA techniques, and serological diagnosis for infectious diseases.',
                'description' => 'Advanced course in immunology and serology covering immune system principles, antigen-antibody reactions, ELISA procedures, rapid diagnostic tests, and interpretation of immunological results. Includes HIV testing, hepatitis serology, and autoimmune disease markers.',
                'learning_objectives' => "• Understand immune system mechanisms\n• Perform ELISA and immunoassays\n• Interpret serological results\n• Conduct HIV and hepatitis testing\n• Apply immunological principles",
                'requirements' => 'Basic immunology knowledge, Laboratory experience, Understanding of infectious diseases',
                'category' => (object)['id' => 5, 'name' => 'Immunology', 'slug' => 'immunology'],
                'instructor' => (object)['id' => 5, 'name' => 'Dr. Sarah Njeri, PhD', 'email' => '<EMAIL>'],
                'status' => 'published',
                'duration' => 42.00,
                'difficulty_level' => 'intermediate',
                'language' => 'English',
                'certificate' => true,
                'is_featured' => false,
                'is_free' => false,
                'price' => 8900.00,
                'discount_price' => 7120.00,
                'thumbnail' => null,
                'preview_video' => null,
                'meta_title' => 'Immunology Serology KMLTTB Course',
                'meta_description' => 'Professional immunology and serology course for medical laboratory technologists. Master immunoassays and diagnostic testing.',
                'tags' => 'immunology,serology,ELISA,HIV testing,hepatitis,autoimmune,KMLTTB',
                'created_at' => '2024-02-05 12:00:00',
                'updated_at' => '2024-02-05 12:00:00',
            ],
            [
                'id' => 6,
                'title' => 'Laboratory Safety and Biosecurity Fundamentals',
                'slug' => 'laboratory-safety-biosecurity-fundamentals',
                'short_description' => 'Essential safety protocols, waste management, and biosecurity measures for medical laboratories in Kenya.',
                'description' => 'Critical course covering laboratory safety protocols, chemical safety, biological hazard management, waste disposal procedures, emergency response, and biosecurity measures. Designed to meet KMLTTB safety requirements and protect laboratory personnel.',
                'learning_objectives' => "• Implement laboratory safety protocols\n• Manage biological and chemical hazards\n• Apply proper waste disposal methods\n• Respond to laboratory emergencies\n• Ensure biosecurity compliance",
                'requirements' => 'Basic laboratory knowledge, No prior safety training required, Commitment to safety practices',
                'category' => (object)['id' => 6, 'name' => 'Laboratory Safety', 'slug' => 'laboratory-safety'],
                'instructor' => (object)['id' => 6, 'name' => 'Dr. Michael Ochieng, MSc Safety', 'email' => '<EMAIL>'],
                'status' => 'published',
                'duration' => 25.00,
                'difficulty_level' => 'beginner',
                'language' => 'English',
                'certificate' => true,
                'is_featured' => true,
                'is_free' => true,
                'price' => 0.00,
                'discount_price' => 0.00,
                'thumbnail' => null,
                'preview_video' => null,
                'meta_title' => 'Laboratory Safety Biosecurity Training',
                'meta_description' => 'Free laboratory safety and biosecurity course for medical laboratory professionals. Learn essential safety protocols.',
                'tags' => 'laboratory safety,biosecurity,waste management,emergency response,KMLTTB,free course',
                'created_at' => '2024-02-10 13:00:00',
                'updated_at' => '2024-02-10 13:00:00',
            ],
        ];
    }

    /**
     * Get static category data for Kenyan Laboratory CPD Provider
     */
    private function getStaticCategoryData()
    {
        return [
            (object)[
                'id' => 1,
                'name' => 'Clinical Chemistry',
                'slug' => 'clinical-chemistry',
                'description' => 'Biochemical analysis, enzyme studies, and clinical chemistry techniques',
                'color' => '#3498db',
                'icon' => 'fas fa-flask',
                'is_active' => true,
                'courses_count' => 1,
            ],
            (object)[
                'id' => 2,
                'name' => 'Hematology',
                'slug' => 'hematology',
                'description' => 'Blood analysis, coagulation studies, and transfusion medicine',
                'color' => '#e74c3c',
                'icon' => 'fas fa-tint',
                'is_active' => true,
                'courses_count' => 1,
            ],
            (object)[
                'id' => 3,
                'name' => 'Microbiology',
                'slug' => 'microbiology',
                'description' => 'Pathogen identification, antimicrobial testing, and infection control',
                'color' => '#2ecc71',
                'icon' => 'fas fa-microscope',
                'is_active' => true,
                'courses_count' => 1,
            ],
            (object)[
                'id' => 4,
                'name' => 'Quality Management',
                'slug' => 'quality-management',
                'description' => 'ISO standards, quality systems, and laboratory accreditation',
                'color' => '#f39c12',
                'icon' => 'fas fa-award',
                'is_active' => true,
                'courses_count' => 1,
            ],
            (object)[
                'id' => 5,
                'name' => 'Immunology',
                'slug' => 'immunology',
                'description' => 'Immunoassays, serology, and diagnostic immunology',
                'color' => '#9b59b6',
                'icon' => 'fas fa-shield-alt',
                'is_active' => true,
                'courses_count' => 1,
            ],
            (object)[
                'id' => 6,
                'name' => 'Laboratory Safety',
                'slug' => 'laboratory-safety',
                'description' => 'Safety protocols, biosecurity, and emergency procedures',
                'color' => '#e67e22',
                'icon' => 'fas fa-hard-hat',
                'is_active' => true,
                'courses_count' => 1,
            ],
        ];
    }
}
