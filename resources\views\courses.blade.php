<!doctype html>
<html class="no-js" lang="en">


<!-- Mirrored from skillgro.websolutionus.com/courses by HTTrack Website Copier/3.x [XR&CO'2014], Wed, 09 Jul 2025 09:37:53 GMT -->
<!-- Added by HTTrack -->
<meta http-equiv="content-type" content="text/html;charset=UTF-8" /><!-- /Added by HTTrack -->

<head>
    <meta charset="utf-8">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    <title>Course || Skillgro</title>
    <meta name="csrf-token" content="LjkuUqlaSDW4X740h7HW6tT5rFW58frkItXzEuOO">
    <meta name="description" content="Course || Skillgro">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- Custom Meta -->
    <!-- Favicon -->
    <link rel="shortcut icon" type="image/x-icon" href="uploads/website-images/favicon.png">
    <!-- CSS here -->
    <link rel="stylesheet" href="frontend/css/bootstrap.min.css">
    <link rel="stylesheet" href="frontend/css/animate.min.css">
    <link rel="stylesheet" href="frontend/css/magnific-popup.css">
    <link rel="stylesheet" href="frontend/css/fontawesome-all.min.css">
    <link rel="stylesheet" href="frontend/css/flaticon-skillgro.css">
    <link rel="stylesheet" href="frontend/css/swiper-bundle.min.css">
    <link rel="stylesheet" href="frontend/css/default-icons.css">
    <link rel="stylesheet" href="frontend/css/select2.min.css">
    <link rel="stylesheet" href="frontend/css/odometer.css">
    <link rel="stylesheet" href="frontend/css/aos.css">
    <link rel="stylesheet" href="frontend/css/plyr.css">
    <link rel="stylesheet" href="frontend/css/spacing.css">
    <link rel="stylesheet" href="frontend/css/tg-cursor.css">
    <link rel="stylesheet" href="frontend/css/bootstrap-datepicker.min.css">
    <link rel="stylesheet" href="global/toastr/toastr.min.css">
    <link rel="stylesheet" href="global/nice-select/nice-select.css">
    <link rel="stylesheet" href="frontend/css/main.minc669.css?v=2.5.0">
    <link rel="stylesheet" href="frontend/css/frontend.minc669.css?v=2.5.0">



    <style>
        :root {
            --tg-theme-primary: #5751e1;
            --tg-theme-secondary: #ffc224;
            --tg-common-color-blue: #050071;
            --tg-common-color-blue-2: #282568;
            --tg-common-color-dark: #1c1a4a;
            --tg-common-color-black: #06042e;
            --tg-common-color-dark-2: #4a44d1;
        }
    </style>


    <script>
        "use strict";
        //write your javascript here without the script tag
    </script>

</head>

<body class="">


    <!-- Scroll-top -->
    <button class="scroll__top scroll-to-target" data-target="html" aria-label="Scroll Top">
        <i class="tg-flaticon-arrowhead-up"></i>
    </button>
    <!-- Scroll-top-end-->

    <!-- header-area -->
    <!-- header-area -->
  @include('includes.header')
    <!-- header-area-end -->
    <!-- header-area-end -->

    <!-- main-area -->
    <main class="main-area fix">
        <!-- breadcrumb-area -->
        <section class="breadcrumb__area breadcrumb__bg" data-background="uploads/website-images/breadcrumb-image.jpg">
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <div class="breadcrumb__content">
                            <h3 class="title">Courses</h3>
                            <nav class="breadcrumb">
                                <span property="itemListElement" typeof="ListItem">
                                    <a href="index.html">Home</a>
                                </span>
                                <span class="breadcrumb-separator"><i class="fas fa-angle-right"></i></span>
                                <span property="itemListElement" typeof="ListItem">
                                    <a href="javascript:;">Courses</a>
                                </span>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
            <div class="breadcrumb__shape-wrap">
                <img src="frontend/img/others/breadcrumb_shape01.svg" alt="img" class="alltuchtopdown">
                <img src="frontend/img/others/breadcrumb_shape02.svg" alt="img" data-aos="fade-right"
                    data-aos-delay="300">
                <img src="frontend/img/others/breadcrumb_shape03.svg" alt="img" data-aos="fade-up" data-aos-delay="400">
                <img src="frontend/img/others/breadcrumb_shape04.svg" alt="img" data-aos="fade-down-left"
                    data-aos-delay="400">
                <img src="frontend/img/others/breadcrumb_shape05.svg" alt="img" data-aos="fade-left"
                    data-aos-delay="400">
            </div>
        </section>
        <!-- breadcrumb-area-end -->

        <!-- all-courses -->
        <section class="all-courses-area section-py-120 top-baseline">
            <div class="container position-relative">
                <div class="preloader-two d-none">
                    <div class="loader-icon-two"><img src="uploads/custom-images/wsus-img-2024-06-06-05-37-49-1116.svg"
                            alt="Preloader"></div>
                </div>
                <div class="row">
                    <div class="col-xl-3 col-lg-4">
                        <div class="courses__sidebar_area">
                            <div class="courses__sidebar_button d-lg-none">
                                <h4>filter</h4>
                            </div>
                            <aside class="courses__sidebar">
                                <div class="courses-widget">
                                    <h4 class="widget-title">Categories</h4>
                                    <div class="courses-cat-list">
                                        <ul class="list-wrap">
                                            @forelse($categories as $category)
                                            <li>
                                                <div class="form-check">
                                                    <input class="form-check-input main-category-checkbox" type="radio"
                                                        name="main_category" value="{{ $category->slug }}" id="cat_{{ $category->id }}">
                                                    <label class="form-check-label" for="cat_{{ $category->id }}">
                                                        {{ $category->name }} ({{ $category->courses_count }})
                                                    </label>
                                                </div>
                                            </li>
                                            @empty
                                            <li>
                                                <div class="form-check">
                                                    <input class="form-check-input main-category-checkbox" type="radio"
                                                        name="main_category" value="clinical-chemistry" id="cat_1">
                                                    <label class="form-check-label" for="cat_1">Clinical Chemistry (1)</label>
                                                </div>
                                            </li>
                                            <li>
                                                <div class="form-check">
                                                    <input class="form-check-input main-category-checkbox" type="radio"
                                                        name="main_category" value="hematology" id="cat_2">
                                                    <label class="form-check-label" for="cat_2">Hematology (1)</label>
                                                </div>
                                            </li>
                                            <li>
                                                <div class="form-check">
                                                    <input class="form-check-input main-category-checkbox" type="radio"
                                                        name="main_category" value="microbiology" id="cat_3">
                                                    <label class="form-check-label" for="cat_3">Microbiology (1)</label>
                                                </div>
                                            </li>
                                            <li>
                                                <div class="form-check">
                                                    <input class="form-check-input main-category-checkbox" type="radio"
                                                        name="main_category" value="quality-management" id="cat_4">
                                                    <label class="form-check-label" for="cat_4">Quality Management (1)</label>
                                                </div>
                                            </li>
                                            <li>
                                                <div class="form-check">
                                                    <input class="form-check-input main-category-checkbox" type="radio"
                                                        name="main_category" value="immunology" id="cat_5">
                                                    <label class="form-check-label" for="cat_5">Immunology (1)</label>
                                                </div>
                                            </li>
                                            <li>
                                                <div class="form-check">
                                                    <input class="form-check-input main-category-checkbox" type="radio"
                                                        name="main_category" value="laboratory-safety" id="cat_6">
                                                    <label class="form-check-label" for="cat_6">Laboratory Safety (1)</label>
                                                </div>
                                            </li>
                                            @endforelse
                                        </ul>
                                        <div class="show-more">
                                        </div>
                                    </div>
                                </div>

                                <div class="sub-category-holder "></div>
                                <div class="courses-widget">
                                    <h4 class="widget-title">Language</h4>
                                    <div class="courses-cat-list">
                                        <ul class="list-wrap">
                                            <li>
                                                <div class="form-check">
                                                    <input class="form-check-input language-checkbox" type="checkbox"
                                                        value="" id="lang">
                                                    <label class="form-check-label" for="lang">All Languages</label>
                                                </div>
                                            </li>
                                            <li>
                                                <div class="form-check">
                                                    <input class="form-check-input language-checkbox" type="checkbox"
                                                        value="English" id="lang_1">
                                                    <label class="form-check-label" for="lang_1">English</label>
                                                </div>
                                            </li>
                                            <li>
                                                <div class="form-check">
                                                    <input class="form-check-input language-checkbox" type="checkbox"
                                                        value="Swahili" id="lang_2">
                                                    <label class="form-check-label" for="lang_2">Swahili</label>
                                                </div>
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="show-more">
                                    </div>
                                </div>
                                <div class="courses-widget">
                                    <h4 class="widget-title">Price</h4>
                                    <div class="courses-cat-list">
                                        <ul class="list-wrap">
                                            <li>
                                                <div class="form-check">
                                                    <input class="form-check-input price-checkbox" type="checkbox"
                                                        value="" id="price_1">
                                                    <label class="form-check-label" for="price_1">All Price</label>
                                                </div>
                                            </li>
                                            <li>
                                                <div class="form-check">
                                                    <input class="form-check-input price-checkbox" type="checkbox"
                                                        value="free" id="price_2">
                                                    <label class="form-check-label" for="price_2">Free</label>
                                                </div>
                                            </li>
                                            <li>
                                                <div class="form-check">
                                                    <input class="form-check-input price-checkbox" type="checkbox"
                                                        value="paid" id="price_3">
                                                    <label class="form-check-label" for="price_3">Paid</label>
                                                </div>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="courses-widget">
                                    <h4 class="widget-title">Skill level</h4>
                                    <div class="courses-cat-list">
                                        <ul class="list-wrap">
                                            <li>
                                                <div class="form-check">
                                                    <input class="form-check-input level-checkbox" type="checkbox"
                                                        value="" id="difficulty_all">
                                                    <label class="form-check-label" for="difficulty_all">All
                                                        Levels</label>
                                                </div>
                                            </li>
                                            <li>
                                                <div class="form-check">
                                                    <input class="form-check-input level-checkbox" type="checkbox"
                                                        value="1" id="difficulty_1">
                                                    <label class="form-check-label" for="difficulty_1">Beginner</label>
                                                </div>
                                            </li>
                                            <li>
                                                <div class="form-check">
                                                    <input class="form-check-input level-checkbox" type="checkbox"
                                                        value="2" id="difficulty_2">
                                                    <label class="form-check-label"
                                                        for="difficulty_2">Intermediate</label>
                                                </div>
                                            </li>
                                            <li>
                                                <div class="form-check">
                                                    <input class="form-check-input level-checkbox" type="checkbox"
                                                        value="3" id="difficulty_3">
                                                    <label class="form-check-label" for="difficulty_3">Advanced</label>
                                                </div>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </aside>
                        </div>
                    </div>
                    <div class="col-xl-9 col-lg-8">
                        <div class="courses-top-wrap courses-top-wrap">
                            <div class="row align-items-center">
                                <div class="col-md-5">
                                    <div class="courses-top-left">
                                        <p>Total <span class="course-count">0</span> courses found
                                        </p>
                                    </div>
                                </div>
                                <div class="col-md-7">
                                    <div class="d-flex justify-content-center align-items-center flex-wrap">
                                        <div class="courses-top-right m-0 ms-md-auto">
                                            <span class="sort-by">Sort By:</span>
                                            <div class="courses-top-right-select">
                                                <select name="orderby" class="orderby">
                                                    <option value="desc">Latest to Oldest</option>
                                                    <option value="asc">Oldest to Latest</option>
                                                </select>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="tab-content" id="myTabContent">
                            <div class="tab-pane fade show active" id="grid" role="tabpanel" aria-labelledby="grid-tab">
                                <div
                                    class="course-holder row courses__grid-wrap row-cols-1 row-cols-xl-3 row-cols-lg-2 row-cols-md-2 row-cols-sm-1">

                                </div>

                                <div class="pagination-wrap">
                                    <div class="pagination">

                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!-- all-courses-end -->
    </main>
    <!-- main-area-end -->

    <!-- modal-area -->
    <!-- Modal -->
    <div class="modal fade dynamic-modal modal-lg" tabindex="-1" aria-labelledby="dynamic-modalLabel" aria-hidden="true"
        data-bs-backdrop='static'>
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
            <div class="modal-content">
                <div class="d-flex justify-content-center align-items:center p-3">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>

            </div>
        </div>
    </div>

    <div class="modal fade bd-example-modal-lg" id="iframeModal" data-bs-backdrop="static" tabindex="-1"
        aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center">
                    <h5>Your are using this website under an external iframe</h5>
                    <p>For a better experience please browse directly instead of an external iframe</p>
                </div>
                <div class="modal-footer justify-content-center">
                    <a target="_blank" href="index.html" class="btn btn-sm btn-primary">Browse Directly</a>
                </div>
            </div>
        </div>
    </div> <!-- Modal -->
    <div class="modal fade" id="exampleModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true"
        data-bs-backdrop='static'>
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h1 class="modal-title fs-5" id="exampleModalLabel">Chapter Title</h1>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form action="https://skillgro.websolutionus.com/instructor/course-chapter//store"
                        class="instructor__profile-form" method="post">
                        <input type="hidden" name="_token" value="LjkuUqlaSDW4X740h7HW6tT5rFW58frkItXzEuOO"
                            autocomplete="off">
                        <div class="col-md-12">
                            <div class="form-grp">
                                <label for="title">Title <code>*</code></label>
                                <input id="title" name="title" type="text" value="">
                            </div>
                        </div>
                        <div class="text-end">
                            <button type="submit" class="btn btn-primary">Create</button>
                        </div>

                    </form>
                </div>
            </div>
        </div>
    </div>
    <!-- modal-area -->

    <!-- footer-area -->
 @include('includes.footer')
    <!-- footer-area-end -->


    <!-- JS here -->
    <script src="global/js/jquery-3.7.1.min.js"></script>
    <script src="frontend/js/proper.min.js"></script>
    <script src="frontend/js/bootstrap.min.js"></script>
    <script src="frontend/js/imagesloaded.pkgd.min.js"></script>
    <script src="frontend/js/jquery.magnific-popup.min.js"></script>
    <script src="frontend/js/jquery.odometer.min.js"></script>
    <script src="frontend/js/jquery.appear.js"></script>
    <script src="frontend/js/tween-max.min.js"></script>
    <script src="frontend/js/select2.min.js"></script>
    <script src="frontend/js/swiper-bundle.min.js"></script>
    <script src="frontend/js/jquery.marquee.min.js"></script>
    <script src="frontend/js/tg-cursor.min.js"></script>
    <script src="frontend/js/svg-inject.min.js"></script>
    <script src="frontend/js/jquery.circleType.js"></script>
    <script src="frontend/js/jquery.lettering.min.js"></script>
    <script src="frontend/js/bootstrap-datepicker.min.js"></script>
    <script src="frontend/js/plyr.min.js"></script>
    <script src="frontend/js/wow.min.js"></script>
    <script src="frontend/js/aos.js"></script>
    <script src="frontend/js/vivus.min.js"></script>
    <script src="global/toastr/toastr.min.js"></script>
    <script src="frontend/js/sweetalert.js"></script>
    <script src="frontend/js/default/frontendc669.js?v=2.5.0"></script>
    <script src="frontend/js/default/cartc669.js?v=2.5.0"></script>
    <script src="global/nice-select/jquery.nice-select.min.js"></script>
    <!-- File Manager js-->
    <script src="vendor/laravel-filemanager/js/stand-alone-button.js"></script>


    <script src="frontend/js/mainc669.js?v=2.5.0"></script>

    <script>
        $('.file-manager').filemanager('file', {
        prefix: 'https://skillgro.websolutionus.com/frontend-filemanager'
    });
    $('.file-manager-image').filemanager('image', {
        prefix: 'https://skillgro.websolutionus.com/frontend-filemanager'
    });

    SVGInject(document.querySelectorAll("img.injectable"));
    </script>

    <!-- dynamic Toastr Notification -->
    <script>
        "use strict";
    toastr.options.closeButton = true;
    toastr.options.progressBar = true;
    toastr.options.positionClass = 'toast-bottom-right';


    $('.datepicker').datepicker({
        format: 'yyyy-mm-dd',
        orientation: "bottom auto"
    });
    </script>


    <!-- Toastr -->


    <!-- Google reCAPTCHA -->

    <!-- tawk -->

    <!-- Cookie Consent -->
    <script src="frontend/js/cookieconsent.min.js"></script>

    <script>
        "use strict";
        window.addEventListener("load", function() {
            window.wpcc.init({
                "border": "normal",
                "corners": "none",
                "colors": {
                    "popup": {
                        "background": "#5751e1",
                        "text": "#fafafa !important",
                        "border": "#5751e1"
                    },
                    "button": {
                        "background": "#fffceb",
                        "text": "#222758"
                    }
                },
                "content": {
                    "href": "https://skillgro.websolutionus.com/page/privacy-policy",
                    "message": "This website uses essential cookies to ensure its proper operation and tracking cookies to understand how you interact with it. The latter will be set only upon approval.",
                    "link": "More Info",
                    "button": "Yes"
                }
            })
        });
    </script>

    <script>
        if ($(".marquee_mode").length) {
        $('.marquee_mode').marquee({
            speed: 20,
            gap: 35,
            delayBeforeStart: 0,
            direction: "left",
            duplicated: true,
            pauseOnHover: true,
            startVisible: true,
        });
    }
    </script>

    <script>
        $(document).on("click", '.wpcc-btn', function() {
        $('.wpcc-container').fadeOut(1000);
    });
    </script>

    <!-- Language Translation Variables -->
    <script>
        var base_url = "{{ url('/') }}";
  var preloader_path = "uploads/custom-images/wsus-img-2024-06-06-05-37-49-1116.svg";

  var demo_mode_error = "This Is Demo Version. You Can Not Change Anything";
  var translation_success = "Translated Successfully!";
  var translation_processing = "Translation Processing, please wait...";
  var search_instructor_placeholder = "Search for an instructor with email or name";
  var Previous = "Previous";
  var Next = "Next";
  var basic_error_message = "Something went wrong";
  var discount = "Discount";
  var subscribe_now = "Subscribe Now";
  var submitting = "Submitting...";
  var submitting = "Submitting...";
  var login_first = "Login first";
    </script>
    <!-- Page specific js -->
    <script src="frontend/js/default/course-page.js"></script>
    <script>
        "use strict";
            //write your javascript here without the script tag
    </script>
</body>




</html>
